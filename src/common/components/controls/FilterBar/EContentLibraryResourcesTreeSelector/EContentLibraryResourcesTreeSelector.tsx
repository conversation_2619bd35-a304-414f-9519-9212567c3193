import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { isEmpty } from 'lodash';

import {
  isEContentLibraryResource,
  resolveNodeId,
  resolveNodeParentId,
  canRender,
  TEContentLibraryResourcesTreeTypes,
} from '../../../../../model/EContentLibraryResourcesTypeNames';
import nodeSortingOptionsSequence from '../../../../utils/nodeSortingOptionsSequence';
import { ICommonTreeSelectorProps } from '../../CommonTreeSelector/CommonTreeSelector';

import eContentLibraryResourcesFilterTreeGql from '../../../../data/eContent/eContentLibraryResourcesFilterTree.graphql';
import Statuses, {
  Active,
  Deleted,
  TStatusWithDraft,
} from '../../../../../model/StatusWithDraft';
import useT from '../../../utils/Translations/useT';
import { IFilterBarComponentProps } from '../FilterBar';
import ResourceSelectorTreeFilters, {
  IResourceSelectorTreeFiltersValue,
} from './ResourceSelectorTreeFilters';

import StatusAddon from '../../../dataViews/NextTree/addons/StatusAddon';
import ActionsAddon, {
  IActionsAddonAction,
} from '../../../dataViews/NextTree/addons/ActionsAddon/ActionsAddon';
import { isRootNode } from '../../../dataViews/DynamicTree';
import Button from '../../Button';
import GqlStaticTreeSelector from '../GqlStaticTreeSelector';
import { catsearch } from '../../../../../common/textSearch';

export interface IEContentLibraryResourcesTreeSelector
  extends IFilterBarComponentProps<TEContentLibraryResourcesTreeTypes> {
  libraryId?: number;
  cookModels?: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  hasFilter?: boolean;
  initialStatus?: TStatusWithDraft | TStatusWithDraft[];
  isSingleSelector?: boolean;
  initialSelectAll?: boolean;
}

const EContentLibraryResourcesTreeSelector: React.FC<
  ICommonTreeSelectorProps<TEContentLibraryResourcesTreeTypes> &
    IEContentLibraryResourcesTreeSelector
> = ({
  libraryId,
  hasFilter,
  initialStatus = [Statuses.Active.value],
  isSingleSelector = true,
  selectionFilter,
  syntheticRootNodeName,
  value,
  onChange,
  initialSelectAll,
  ...props
}) => {
  const t = useT();
  const [allModels, setAllModels] = useState();
  const [filterItems, setFilterItems] = useState<
    TEContentLibraryResourcesTreeTypes | undefined
  >();
  const defaultFilter = {
    status: initialStatus,
    searchQuery: '',
  };
  const [
    filterValues,
    setFilterValues,
  ] = useState<IResourceSelectorTreeFiltersValue>(defaultFilter);
  const disabled = false;

  useEffect(() => {
    if (onChange && filterItems && !disabled) {
      const _filterItems = filterItems;
      onChange(_filterItems);
      setFilterItems(undefined);
      setFilterValues({ ...filterValues, searchQuery: '' });
    }
  }, [disabled, filterValues, onChange, filterItems]);

  const _onChange = useCallback(
    selectedItems => {
      if (isSingleSelector) setFilterItems(selectedItems);
      else {
        const filtered = selectedItems.filter(
          node => node.status === filterValues.status,
        );
        setFilterItems(filtered);
      }
    },
    [isSingleSelector, filterValues.status],
  );

  const [isAllSelected, setIsAllSelected] = useState(false);

  useEffect(() => {
    if (allModels && filterItems) {
      const check =
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        allModels?.filter(
          x => isEContentLibraryResource(x) && x.status === filterValues.status,
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
        ).length === filterItems?.length;
      setIsAllSelected(check);
    }
  }, [allModels, filterItems, filterValues.status]);

  const renderNodeAddons = (node, meta) => {
    const { id } = node;
    const { handleClear } = meta;

    const handleClearFn = e => {
      handleClear();
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
    };

    const addons: JSX.Element[] = [];
    const actions: IActionsAddonAction[] = [];
    if (isRootNode(node)) {
      addons.push(
        <Button
          additionClasses="text-primary no-padding-top no-padding-bottom"
          buttonStyle="link"
          onClick={handleClearFn}
        >
          {t('Clear')}
        </Button>,
      );
    }
    if (!isRootNode(node)) {
      addons.push(
        <StatusAddon
          key={`status_${id}`}
          customActiveStatus={Active}
          customDeletedStatus={Deleted}
          customStatuses={Statuses.BasicByValue}
          status={node.status}
        />,
      );
    }

    if (!isEmpty(actions)) {
      addons.push(
        <ActionsAddon
          key={`actions_${node.id}`}
          actions={actions}
          node={node}
        />,
      );
    }

    return addons;
  };

  const nodeIsVisible = useCallback(
    node =>
      isEContentLibraryResource(node)
        ? node.status === filterValues.status
        : true,
    [filterValues.status],
  );

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'searchQuery',
        predicate: value => node =>
          catsearch(node.name, value) && node.status === filterValues.status,
        includeChildren: true,
        collectVisibleIds: true,
      },
    ],
    [filterValues.status],
  );

  return (
    <GqlStaticTreeSelector
      hasSyntheticRootNode
      adapter={{
        renderNodeAddons,
        resolveNodeId,
        resolveNodeParentId,
        nodeIsLeaf: isEContentLibraryResource,
        canRender,
      }}
      disabled={!libraryId || disabled}
      filter={{
        component: ResourceSelectorTreeFilters,
        value: filterValues,
        onChange: setFilterValues,
        predicatesMeta,
      }}
      gql={eContentLibraryResourcesFilterTreeGql}
      gqlSkip={!libraryId}
      gqlVariables={{ libraryId, status: Statuses.BasicProps }}
      hasClearButton={false}
      hasFilter={hasFilter}
      initialSelectAll={initialSelectAll}
      isAllSelected={isAllSelected}
      isSingleSelector={isSingleSelector}
      name="EContentLibraryResourcesTreeSelector"
      plugins={{
        nodeIsVisible,
        nodeSortingOptions: nodeSortingOptionsSequence,
      }}
      selectionFilter={
        selectionFilter as
          | ((
              items: TEContentLibraryResourcesTreeTypes[],
            ) => TEContentLibraryResourcesTreeTypes[])
          | undefined
      }
      setAllModels={setAllModels}
      syntheticRootNodeName={syntheticRootNodeName as string | undefined}
      title={t('Resource')}
      tree="EContentLibraryResourcesTreeSelector"
      value={value}
      onChange={_onChange}
    />
  );
};

export default EContentLibraryResourcesTreeSelector;
