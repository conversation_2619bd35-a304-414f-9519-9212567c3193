import { get, keyBy } from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';
import {
  nodePropType,
  treeAdapterPropTypes,
  treePluginPropTypes,
} from '../propTypes';
import { deinitializeTree } from '../store/actions';
import TreeBody from '../TreeBody/TreeBody';

import withNextTreeStoreConnect from '../store/withNextTreeStoreConnect';

import TreeContext from '../TreeContext';

@withNextTreeStoreConnect(
  (state, { tree: treeId }) => ({
    treeState: state[treeId],
  }),
  (dispatch, { tree, adapter, plugins }) => ({
    tearDown: () => dispatch(deinitializeTree(tree, { adapter, plugins })),
  }),
)
export default class TreeContent extends React.Component {
  static indexPlugins(plugins) {
    return {
      allPlugins: plugins,
      plugins: keyBy(plugins, 'key'),
    };
  }

  static propTypes = {
    tree: PropTypes.string.isRequired,

    adapter: treeAdapterPropTypes.isRequired,

    plugins: PropTypes.arrayOf(treePluginPropTypes).isRequired,

    children: PropTypes.node,

    models: PropTypes.arrayOf(PropTypes.any),

    filter: PropTypes.any,

    context: PropTypes.any,

    selection: PropTypes.oneOfType([
      nodePropType,
      PropTypes.arrayOf(nodePropType),
    ]),

    onSelectionChange: PropTypes.func,
    handleClear: PropTypes.func,

    treeState: PropTypes.object,

    tearDown: PropTypes.func.isRequired,

    hasBottomPadding: PropTypes.bool,

    isScrollDisabled: PropTypes.bool,
  };

  static defaultProps = {
    children: null,
    models: null,
    filter: null,
    context: null,
    selection: null,
    treeState: null,
    onSelectionChange: null,
    handleClear: null,
    hasBottomPadding: true,
    isScrollDisabled: false,
  };

  constructor(props) {
    super(props);

    this.state = { ...TreeContent.indexPlugins(props.plugins) };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    if (nextProps.plugins === prevState.allPlugins) {
      return null;
    }
    return { ...TreeContent.indexPlugins(nextProps.plugins) };
  }

  componentWillUnmount() {
    //Todo fix issue. if treeId was changed old tree will be keeped on store
    const { tearDown } = this.props;
    tearDown();
  }

  hasChildren = node => {
    const { adapter, treeState } = this.props;

    const nodeId = adapter.nodeId(node);

    const children = get(treeState, `children.${nodeId}`, []);

    return !!children.length;
  };

  render() {
    const {
      children,
      tree,
      adapter,
      models,
      filter,
      selection,
      context,
      onSelectionChange,
      handleClear,
      treeState,
      hasBottomPadding,
      isScrollDisabled,
    } = this.props;

    const { plugins } = this.state;

    const contextValue = {
      tree,
      adapter,
      models,
      plugins,
      filter,
      selection,
      context,
      onSelectionChange,
      handleClear,
      treeState,
      hasBottomPadding,
      isScrollDisabled,
      hasChildren: this.hasChildren,
    };

    const content = children ? children : <TreeBody />;

    return (
      <TreeContext.Provider value={contextValue}>
        {content}
      </TreeContext.Provider>
    );
  }
}
