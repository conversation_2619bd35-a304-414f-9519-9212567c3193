import { isFunction, isEmpty } from 'lodash';
import { getNodeId } from '../store/actionHandlers/common/adapterActions';

export default class SingleSelection {
  constructor({ nodeIsSelectable = true } = {}) {
    this._nodeIsSelectable = isFunction(nodeIsSelectable)
      ? nodeIsSelectable
      : () => nodeIsSelectable;
  }

  get key() {
    return 'selection';
  }

  get selectionVisuals() {
    return 'box';
  }

  prepareTreeState(treeState) {
    treeState.selectedNode = null;
    treeState.selectedNodeId = null;
  }

  getSelection(treeState) {
    return treeState.selectedNode;
  }

  setSelection(treeState, selection, { adapter }) {
    treeState.selectedNode = selection;
    treeState.selectedNodeId =
      (!isEmpty(selection) && getNodeId(adapter, selection)) || null;
  }

  nodeIsSelected(treeState, nodeId) {
    return treeState.selectedNodeId === nodeId || false;
  }

  nodeIsSelectable(node, selection) {
    return this._nodeIsSelectable(node, selection);
  }

  toggleNodeSelected(treeState, nodeId) {
    const { models } = treeState;

    return {
      ...treeState,
      selectedNode: models[nodeId],
      selectedNodeId: nodeId,
    };
  }
}
