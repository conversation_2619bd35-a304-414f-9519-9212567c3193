import { isFunction } from 'lodash';
import { isRootNode } from '../../DynamicTree';

import { getNodeId } from '../store/actionHandlers/common/adapterActions';

const SELECTED = {
  selected: true,
  someChildrenSelected: true,
  allChildrenSelected: true,
};

const DESELECTED = {
  selected: false,
  someChildrenSelected: false,
  allChildrenSelected: false,
};

export default class MultiSelection {
  constructor({ nodeIsSelectable = () => true } = {}) {
    this._nodeIsSelectable = isFunction(nodeIsSelectable)
      ? nodeIsSelectable
      : () => nodeIsSelectable;
  }

  get key() {
    return 'selection';
  }

  get selectionVisuals() {
    return 'checkbox';
  }

  prepareTreeState(treeState) {
    treeState.singleSelectionMode = false;
    treeState.selection = [];
    treeState.multiselectStatuses = new Map();
  }

  getSelection(treeState) {
    return treeState.selection;
  }

  setSelection(treeState, selection, { adapter }) {
    treeState.selection = selection;
    treeState.multiselectStatuses = new Map();
    const { multiselectStatuses } = treeState;

    if (selection) {
      for (const node of selection) {
        const nodeId = getNodeId(adapter, node);
        // SETTING ONLY SELECTED NODES WITHOUT NESTED CHILDREN
        multiselectStatuses.set(nodeId, SELECTED);
        // setChildrenSelection(nodeId, treeState, SELECTED);
        recomputeParents(nodeId, treeState);
      }
    }
  }

  nodeIsSelected(treeState, nodeId) {
    return (
      this._nodeIsSelectable(treeState.models[nodeId], treeState.selection) &&
      (multiselectStatus(nodeId, treeState.multiselectStatuses).selected ||
        multiselectStatus(nodeId, treeState.multiselectStatuses)
          .allChildrenSelected)
    );
  }

  nodeIsAdvanced(treeState, nodeId) {
    const { selected, allChildrenSelected } = multiselectStatus(
      nodeId,
      treeState.multiselectStatuses,
    );
    return !selected && allChildrenSelected;
  }

  nodeIsIntermediate({ multiselectStatuses, context, children }, nodeId) {
    const { selected, someChildrenSelected } = multiselectStatus(
      nodeId,
      multiselectStatuses,
    );

    const childrenCount = context?.getMainTreeChildrenCounts
      ? context.getMainTreeChildrenCounts(nodeId)
      : 0;

    const childIds = children[nodeId];

    if (
      Array.isArray(childIds) &&
      childIds.length < childrenCount &&
      someChildrenSelected
    ) {
      return true;
    }

    return !selected && someChildrenSelected;
  }

  nodeIsSelectable(node, selection) {
    return this._nodeIsSelectable(node, selection);
  }

  toggleNodeSelected(oldTreeState, nodeId) {
    if (this.nodeIsSelected(oldTreeState, nodeId)) {
      return {
        ...oldTreeState,
        ...deselectNode(oldTreeState, nodeId, this._nodeIsSelectable),
      };
    }
    return {
      ...oldTreeState,
      ...selectNode(oldTreeState, nodeId, this._nodeIsSelectable),
    };
  }
}

function selectNode(oldTreeState, nodeId, _nodeIsSelectable) {
  const {
    models,
    parents,
    children,
    multiselectStatuses: oldMultiselectStatuses,
    selection: oldSelection,
  } = oldTreeState;

  const multiselectStatuses = new Map(oldMultiselectStatuses);

  setChildrenSelection(
    nodeId,
    { ...oldTreeState, multiselectStatuses, children },
    SELECTED,
  );

  recomputeParents(nodeId, { ...oldTreeState, multiselectStatuses });

  return {
    selection: collectSelection(
      models,
      multiselectStatuses,
      children,
      parents,
      oldSelection,
      _nodeIsSelectable,
    ),
    multiselectStatuses,
  };
}

function deselectNode(oldTreeState, nodeId, _nodeIsSelectable) {
  const {
    models,
    parents,
    children,
    multiselectStatuses: oldMultiselectStatuses,
    selection: oldSelection,
  } = oldTreeState;

  const multiselectStatuses = new Map(oldMultiselectStatuses);

  setChildrenSelection(
    nodeId,
    { ...oldTreeState, multiselectStatuses, children },
    null,
  );

  recomputeParents(nodeId, { ...oldTreeState, multiselectStatuses });

  return {
    selection: collectSelection(
      models,
      multiselectStatuses,
      children,
      parents,
      oldSelection,
      _nodeIsSelectable,
    ),
    multiselectStatuses,
  };
}

function setChildrenSelection(nodeId, treeState, status) {
  const { multiselectStatuses, children, context } = treeState;
  if (status) {
    multiselectStatuses.set(nodeId, status);
  } else {
    multiselectStatuses.delete(nodeId);
  }

  const childIds = children[nodeId];
  if (Array.isArray(childIds)) {
    const childrenCount = context?.getMainTreeChildrenCounts
      ? context.getMainTreeChildrenCounts(nodeId)
      : 0;

    const allChildrenSelected = children[nodeId].every(id => {
      const statuses = multiselectStatus(id, multiselectStatuses);
      return statuses.selected && statuses.allChildrenSelected;
    });

    const someChildrenSelected = children[nodeId].some(id => {
      const statuses = multiselectStatus(id, multiselectStatuses);
      return statuses.selected || statuses.someChildrenSelected;
    });

    if (
      status &&
      (childIds.length < childrenCount ||
        (!allChildrenSelected && someChildrenSelected))
    ) {
      multiselectStatuses.set(nodeId, {
        selected: allChildrenSelected,
        allChildrenSelected: false,
        someChildrenSelected: true,
      });
    }

    for (const childId of childIds) {
      setChildrenSelection(
        childId,
        { ...treeState, multiselectStatuses, children },
        status,
      );
    }

    const newAllChildrenSelected = children[nodeId].every(id => {
      const statuses = multiselectStatus(id, multiselectStatuses);
      return statuses.selected && statuses.allChildrenSelected;
    });

    if (status && newAllChildrenSelected && childIds.length === childrenCount) {
      multiselectStatuses.set(nodeId, SELECTED);
    }
  }
}

function recomputeParents(nodeId, treeState) {
  const { multiselectStatuses, parents, children, context } = treeState;
  const parentId = parents[nodeId];
  if (!parentId) {
    return;
  }

  const childrenCount = context?.getMainTreeChildrenCounts
    ? context.getMainTreeChildrenCounts(parentId)
    : 0;

  const parentStatus = multiselectStatus(parentId, multiselectStatuses);

  let allChildrenSelected = children[parentId].every(id => {
    const statuses = multiselectStatus(id, multiselectStatuses);
    return statuses.selected && statuses.allChildrenSelected;
  });

  let someChildrenSelected = children[parentId].some(id => {
    const statuses = multiselectStatus(id, multiselectStatuses);
    return statuses.selected || statuses.someChildrenSelected;
  });

  if (children[parentId].length < childrenCount) {
    allChildrenSelected = false;
    someChildrenSelected = true;
  }

  if (
    parentStatus.allChildrenSelected !== allChildrenSelected ||
    parentStatus.someChildrenSelected !== someChildrenSelected
  ) {
    multiselectStatuses.set(parentId, {
      selected: allChildrenSelected,
      allChildrenSelected,
      someChildrenSelected,
    });
    recomputeParents(parentId, { ...treeState, multiselectStatuses });
  }
}

function collectSelection(
  models,
  multiselectStatuses,
  children,
  parents,
  oldSelection,
  _nodeIsSelectable,
) {
  const result = [];

  for (const [id, status] of multiselectStatuses) {
    if (status.selected) {
      if (!isRootNode(models[id]) || _nodeIsSelectable(models[id])) {
        result.push(models[id] || oldSelection.find(org => org.id === id));
      }
    }
  }

  return result.filter(node => !!node);
}

function multiselectStatus(nodeId, multiselectStatuses) {
  return multiselectStatuses.get(nodeId) || DESELECTED;
}
