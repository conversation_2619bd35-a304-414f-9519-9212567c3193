import beginChangingExpandNode from './actionHandlers/beginChangingExpandNode';
import beginLazyLoading from './actionHandlers/beginLazyLoading';
import deinitializeTree from './actionHandlers/deinitializeTree';
import finishChangingExpandNode from './actionHandlers/finishChangingExpandNode';

import initializeTree from './actionHandlers/initializeTree';
import resetSelection from './actionHandlers/resetSelection';
import toggleSelected from './actionHandlers/toggleSelected';
import updateLazyLoading from './actionHandlers/updateLazyLoading';
import updateTree from './actionHandlers/updateTree';
import {
  BEGIN_LAZY_LOADING,
  BEGIN_TOGGLE_EXPAND,
  DEINITIALIZE,
  END_TOGGLE_EXPAND,
  INITIALIZE,
  RESET_SELECTION,
  TOGGLE_SELECTED,
  UPDATE,
  UPDATE_LAZY_LOADING,
} from './constants';

const Actions = {
  [INITIALIZE]: initializeTree,
  [DEINITIALIZE]: deinitializeTree,
  [UPDATE]: updateTree,
  [BEGIN_TOGGLE_EXPAND]: beginChangingExpandNode,
  [END_TOGGLE_EXPAND]: finishChangingExpandNode,
  [TOGGLE_SELECTED]: toggleSelected,
  [BEGIN_LAZY_LOADING]: beginLazyLoading,
  [UPDATE_LAZY_LOADING]: updateLazyLoading,
  [RESET_SELECTION]: resetSelection,
};

export default function createTreeReducer() {
  return (state = {}, action) => {
    const handler = Actions[action.type];
    return (handler && handler(state, action)) || state;
  };
}
