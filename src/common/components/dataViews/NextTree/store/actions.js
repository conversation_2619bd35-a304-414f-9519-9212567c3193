import {
  BEGIN_TOGGLE_EXPAND,
  DEINITIALIZE,
  END_TOGGLE_EXPAND,
  INITIALIZE,
  RESET_SELECTION,
  TOGGLE_SELECTED,
  UPDATE,
} from './constants';

export const initializeTree = (
  treeId,
  { models, selection, adapter, plugins, filter, context },
) => (dispatch, getState) => {
  dispatch({
    type: INITIALIZE,
    treeId,
    models,
    selection,
    adapter,
    plugins,
    filter,
    context,
  });
  const treeState = getState()[treeId];

  const { lazyLoading } = plugins;

  if (treeState && lazyLoading) {
    lazyLoading.loadExpandedNodes(treeState, models, dispatch, {
      plugins,
      adapter,
    });
  }
};

export const updateTree = (
  treeId,
  models,
  filter,
  context,
  { adapter, plugins },
) => (dispatch, getState) => {
  dispatch({
    type: UPDATE,
    treeId,
    models,
    filter,
    context,
    adapter,
    plugins,
  });

  const treeState = getState()[treeId];

  const { lazyLoading } = plugins;

  if (treeState && lazyLoading) {
    lazyLoading.loadExpandedNodes(treeState, models, dispatch, {
      plugins,
      adapter,
    });
  }
};

export const deinitializeTree = (treeId, { adapter, plugins }) => ({
  type: DEINITIALIZE,
  treeId,
  adapter,
  plugins,
});

export const finishExpandAnimation = (
  treeId,
  nodeId,
  expanded,
  { adapter, plugins },
) => ({
  type: END_TOGGLE_EXPAND,
  treeId,
  nodeId,
  expanded,
  adapter,
  plugins,
});

export const toggleNodeSelected = (treeId, nodeId, { adapter, plugins }) => ({
  type: TOGGLE_SELECTED,
  treeId,
  nodeId,
  adapter,
  plugins,
});

export const setNodeExpanded = (
  treeId,
  nodeId,
  expanded,
  { adapter, plugins },
  reload = false,
) => (dispatch, getState) => {
  const treeState = getState()[treeId];

  if (!treeState) {
    return;
  }

  dispatch({
    type: BEGIN_TOGGLE_EXPAND,
    treeId,
    nodeId,
    expanded,
    adapter,
    plugins,
  });

  const { lazyLoading } = plugins;
  if (
    !!expanded &&
    lazyLoading &&
    (reload || lazyLoading.shouldInitiateLoading(treeState, nodeId))
  ) {
    lazyLoading.initiateLoading(treeState, nodeId, dispatch, {
      adapter,
      plugins,
    });
  }
};

export const resetTreeSelection = (
  treeId,
  selection,
  { adapter, plugins },
) => ({
  type: RESET_SELECTION,
  treeId,
  selection,
  adapter,
  plugins,
});
