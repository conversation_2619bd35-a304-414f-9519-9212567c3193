export const ReducerName = 'dataTree';

export const INITIALIZE = `${ReducerName}/INITIALIZE`;
export const DEINITIALIZE = `${ReducerName}/DEINITIALIZE`;
export const UPDATE = `${ReducerName}/UPDATE`;
export const BEGIN_TOGGLE_EXPAND = `${ReducerName}/BEGIN_TOGGLE_EXPAND`;
export const END_TOGGLE_EXPAND = `${ReducerName}/END_TOGGLE_EXPAND`;
export const TOGGLE_SELECTED = `${ReducerName}/TOGGLE_SELECTED`;
export const BEGIN_LAZY_LOADING = `${ReducerName}/BEGIN_LAZY_LOADING`;
export const UPDATE_LAZY_LOADING = `${ReducerName}/UPDATE_LAZY_LOADING`;
export const RESET_SELECTION = `${ReducerName}/RESET_SELECTION`;
