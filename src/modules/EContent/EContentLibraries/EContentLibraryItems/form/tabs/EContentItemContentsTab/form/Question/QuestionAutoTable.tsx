import React, { useCallback, useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { filter, get, isNumber, map, unset } from 'lodash';

import tableColumnsAuto from './tableColumnsAuto';
import useT from '../../../../../../../../../common/components/utils/Translations/useT';
import StaticTable from '../../../../../../../../../common/components/dataViews/Tables/StaticTable';
import IEAutoQuestion from '../../../../../../../../../common/abstract/EContent/IEAutoQuestion';
import { ISimpleTableConfig } from '../../../../../../../../../common/propTypes';
import RoundedLinkButton from '../../../../../../../../../common/components/controls/RoundedLinkButton';
import styles from './QuestionAutoGenerate.scss';
import useEntityFormContext from '../../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import Checkbox from '../../../../../../../../../common/components/controls/base/Checkbox';
import Scroll from '../../../../../../../../../common/components/other/Scroll';

import {
  ESSAY_QUESTION,
  MULTI_SELECT,
  NUMBER,
  NUMBER_QUESTION,
  PARAGRAPH_QUESTION,
  SHORT_TEXT,
  SINGLE_SELECT,
  TRUEFALSE,
} from '../../../../../../../../../model/QuestionType';

const QuestionAutoTable: React.FC<{
  items: IEAutoQuestion[];
  onClearQuestion: () => void;
  onAgResponseShow: () => void;
  onChangeValue: (e: any) => void;
  tickedItems: boolean[];
  setTickedItems: React.Dispatch<React.SetStateAction<boolean[]>>;
}> = ({
  items,
  onClearQuestion,
  onChangeValue,
  onAgResponseShow,
  tickedItems,
  setTickedItems,
}) => {
  const { values, setFieldValue } = useEntityFormContext();
  const [radioOptionItems, setRadioOptionItems] = useState<number[]>([]);
  const [checkOptionItems, setCheckOptionItems] = useState<number[][]>([]);
  const [tick, setTick] = useState(false);
  const toggleOptionRadio = useCallback(
    (id: number, key: number) => {
      radioOptionItems[id] = key;
      const newItems = [...radioOptionItems];
      newItems[id] = key;
      setRadioOptionItems(newItems);
    },
    [values, setFieldValue, radioOptionItems],
  );
  const toggleOptionCheck = useCallback(
    (id: number, key: number) => {
      setCheckOptionItems(preItem => {
        if (preItem[id].includes(key)) {
          preItem[id] = filter(preItem[id], item => item !== key);
        } else {
          preItem[id].push(key);
        }
        return preItem;
      }),
        map(
          filter(values, item => item.id === id),
          item => setFieldValue(`${item.id}.tick`, !item.tick),
        );
    },
    [values, setFieldValue],
  );
  const onToggleTick = useCallback(
    (id: number) => {
      setTickedItems(preItem =>
        map(preItem, (item, key) => (key === id ? !item : item || false)),
      );
      map(
        filter(values, item => item.id === id),
        item => setFieldValue(`${item.id}.tick`, !item.tick),
      );
    },
    [values, setFieldValue, tickedItems],
  );
  const onToggleTickAll = useCallback(
    (checked: boolean) => {
      setTickedItems(preItem => map(preItem, item => checked));
      map(values, (item, key) => {
        setFieldValue(`${key}.tick`, checked);
      });
    },
    [values, setFieldValue, tickedItems],
  );

  const _onToggleTickAll = useCallback(
    event => {
      setTick(!tick);
      onToggleTickAll(!tick);
    },
    [tick, onToggleTickAll],
  );

  useEffect(() => {
    const changeVal = map(Object.keys(values), key => {
      if ([MULTI_SELECT.id].includes(values[key].questionType)) {
        values[key].answer = checkOptionItems[key];
      } else if (
        [SINGLE_SELECT.id, TRUEFALSE.id].includes(values[key].questionType)
      ) {
        values[key].answer = [radioOptionItems[key]];
      }
      values[key].tick = tickedItems[key];
      values[key].questionOptions = [
        NUMBER_QUESTION.id,
        SHORT_TEXT.id,
        PARAGRAPH_QUESTION.id,
        ESSAY_QUESTION.id,
      ].includes(values[key].questionType)
        ? []
        : values[key].questionOptions;
      return values[key];
    });
    onChangeValue(changeVal);
  }, [values, tickedItems, radioOptionItems, checkOptionItems]);
  useEffect(() => {
    const ticketOptionItemsList: number[] = [];
    const checkOptionItemsList: number[][] = [];
    setTickedItems(map(items, 'tick'));
    map(items, (item, key) => {
      setFieldValue(`${key}.id`, key);
      setFieldValue(`${key}.tick`, false);
      setFieldValue(`${key}.questionType`, item.questionType);
      setFieldValue(`${key}.questionTitle`, item.questionTitle);
      setFieldValue(`${key}.questionCorrectAnswer`, item.questionCorrectAnswer);
      map(item.questionOptions, (option, key2) => {
        setFieldValue(`${key}.questionOptions.${key2}`, option);
        setFieldValue(`${key}.questionOptions.${key2}`, option);
      });
      setFieldValue(`${key}.questionLevel`, item.questionLevel);
      if (
        item.questionType &&
        [SINGLE_SELECT.id, TRUEFALSE.id, NUMBER.id].includes(item.questionType)
      ) {
        ticketOptionItemsList.push(
          item.questionAnswer && isNumber(item.questionAnswer[0])
            ? Number(item.questionAnswer[0])
            : -1,
        );
      } else {
        ticketOptionItemsList.push(-1);
      }
      if (item.questionType && [MULTI_SELECT.id].includes(item.questionType)) {
        checkOptionItemsList[key] =
          map(item.questionAnswer, item => Number(item)) || [];
      }
      if (
        item.questionType &&
        [NUMBER_QUESTION.id, SHORT_TEXT.id, PARAGRAPH_QUESTION.id].includes(
          item.questionType,
        )
      ) {
        setFieldValue(`${key}.answer`, item.questionCorrectAnswer);
      }

      if (
        item.questionType &&
        [
          NUMBER_QUESTION.id,
          SHORT_TEXT.id,
          PARAGRAPH_QUESTION.id,
          ESSAY_QUESTION.id,
        ].includes(item.questionType)
      ) {
        setFieldValue(`${key}.answerMaxLimit`, item.answerMaxLimit);
      }
    });
    if (checkOptionItemsList.length > 0) {
      setCheckOptionItems(checkOptionItemsList);
    }
    if (ticketOptionItemsList.length > 0) {
      setRadioOptionItems(ticketOptionItemsList);
    }
  }, [items]);

  const t = useT();
  const onTickAllCheckbox = useMemo(
    () => <Checkbox value={tick} onChange={_onToggleTickAll} />,
    [tick, _onToggleTickAll],
  );

  const columns = useMemo(
    () =>
      tableColumnsAuto(
        t,
        toggleOptionRadio,
        radioOptionItems,
        toggleOptionCheck,
        checkOptionItems,
        onToggleTick,
        onTickAllCheckbox,
        tickedItems,
      ),
    [
      t,
      toggleOptionRadio,
      radioOptionItems,
      toggleOptionCheck,
      checkOptionItems,
      onToggleTick,
      onTickAllCheckbox,
      tickedItems,
    ],
  );
  const tableConfig = useMemo<ISimpleTableConfig<IEAutoQuestion>>(
    () => ({
      columns,
      getId: ({ id }) => id,
    }),
    [columns],
  );

  const selectedItems = useCallback(() => {
    const total = items.length;
    const totalTicked = tickedItems.filter(Boolean).length;
    return (
      <p className={classNames('mb-0')}>
        {totalTicked
          ? t(`Selected #{totalTicked} of #{total}`, {
              totalTicked,
              total,
            })
          : null}
      </p>
    );
  }, [items, tickedItems, t]);

  return (
    <div className={styles.flexCol}>
      <div className={classNames(styles.showMobile)}>
        <div className={classNames(styles.flex, 'pl-15 pr-15 pt-5 pb-5')}>
          {selectedItems()}
          <div>
            <RoundedLinkButton
              additionClasses={classNames(
                'legitRipple ml-10 pull-left',
                styles.showMobile,
                styles.noSpace,
              )}
              type="button"
              onClick={onAgResponseShow}
            >
              {t('Response')}
            </RoundedLinkButton>
            <RoundedLinkButton
              additionClasses={classNames(
                'legitRipple ml-10 pull-right',
                styles.showMobile,
                styles.noSpace,
              )}
              type="button"
              onClick={onClearQuestion}
            >
              {t('Clear')}
            </RoundedLinkButton>
          </div>
        </div>
      </div>

      <StaticTable
        hasScroll={false}
        config={tableConfig}
        items={items}
        tableHeaderClasses={classNames(styles.questionTableStickyHeader)}
        tableWrapperClasses={classNames(
          styles.questionTableSticky,
          styles.scrollbarContainer,
          'w-100-i',
        )}
      />
    </div>
  );
};

export default QuestionAutoTable;
