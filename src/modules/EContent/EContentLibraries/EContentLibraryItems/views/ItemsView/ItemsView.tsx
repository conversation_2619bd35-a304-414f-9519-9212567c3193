import React, { useCallback, useMemo, useState } from 'react';
import { filter, map, get, isNil } from 'lodash';
import { useHistory, useRouteMatch, useLocation } from 'react-router-dom';

import useT from '../../../../../../common/components/utils/Translations/useT';
import GqlFullCrudTable from '../../../../../../common/components/dataViews/GqlFullCrudTable';
import eContentItem from '../../../../../../common/data/eContent/eContentItem.graphql';
import eContentItems from '../../../../../../common/data/eContent/eContentItems.graphql';
import eContentItemsCount from '../../../../../../common/data/eContent/eContentItemsCount.graphql';
import createEContentItem from '../../../../../../common/data/eContent/createEContentItem.graphql';
import updateEContentItem from '../../../../../../common/data/eContent/updateEContentItem.graphql';
import deleteEContentItem from '../../../../../../common/data/eContent/deleteEContentItem.graphql';

import FilterBar from '../../../../../../common/components/controls/FilterBar';
import StatusWithDraft, {
  Active,
} from '../../../../../../model/StatusWithDraft';
import useLanguages from '../../../../../../common/data/hooks/useLanguages';
import cookColumns from './tableColumns';
import EContentItemForm from '../../form';
import IEContentResource from '../../../../../../common/abstract/EContent/IEContentResource';
import IEContentItem from '../../../../../../common/abstract/EContent/IEContentItem';
import {
  isEContentLibraryResource,
  TEContentLibraryResourcesTreeTypes,
} from '../../../../../../model/EContentLibraryResourcesTypeNames';
import ILanguage from '../../../../../../common/abstract/ILanguage';
import {
  DefaultPageSize,
  MinPageSize,
} from '../../../../../../common/components/other/PageSizeSelect/pageSizes';
import usePaginationRouting from '../../../../../../common/data/hooks/usePaginationRouting';

export interface IItemsView {
  selectedResources?: Partial<IEContentResource>[];
  libraryId?: number;
  libraryName?: string;
  usedKeywords?: string[];
  isListItemQueryPrevented(variables: object): boolean;
  goToCreate(): void;
  cookQueryVariables: Function;
  rightActionButtons: ((props: object) => JSX.Element) | JSX.Element | null;
  onFilterChange: (filter: object) => void;
  filterKey: string;
  cookFilterModels: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  filterLanguages: (options: ILanguage[], values: object) => ILanguage[];
  parentUrl?: string;
}

const ItemsView: React.FC<IItemsView> = ({
  selectedResources,
  libraryId,
  libraryName,
  usedKeywords,
  isListItemQueryPrevented,
  goToCreate,
  cookQueryVariables,
  rightActionButtons,
  onFilterChange,
  filterKey,
  cookFilterModels,
  filterLanguages,
}) => {
  const t = useT();
  const history = useHistory();
  const match = useRouteMatch();
  const location = useLocation();
  const columns = useMemo(() => cookColumns(t, libraryId), [t, libraryId]);
  const [filter, setFilter] = useState();

  const { languages } = useLanguages();
  const allLanguageIds = useMemo(() => map(languages, 'id'), [languages]);
  const allStatusValues = useMemo(() => StatusWithDraft.BasicProps, []);

  const languagesMultiSelector = useCallback(
    props => (
      <FilterBar.LanguagesMultiSelector
        {...props}
        isEmptyAllowed
        filterOptions={filterLanguages}
        value={isNil(props?.value) ? allLanguageIds : props?.value}
      />
    ),
    [filterLanguages, allLanguageIds],
  );

  const statusWithDraftMultiSelector = useCallback(
    props => {
      let defaultValue;
      if (isNil(props?.value) || props?.value === 'ACTIVE') {
        defaultValue = allStatusValues;
      } else {
        defaultValue = props?.value;
      }

      return (
        <FilterBar.StatusWithDraftMultiSelector
          {...props}
          isEmptyAllowed
          value={defaultValue}
        />
      );
    },
    [allStatusValues],
  );
  const { pageSize: _pageSize, pageNumber: _pageNumber } = usePaginationRouting(
    {
      defaultPageSize: DefaultPageSize,
      defaultPageNumber: 1,
    },
  );

  const handleGoBack = useCallback(() => {
    const count = get(filter, 'count', MinPageSize);
    const page = get(filter, 'first', 0) / count + 1;
    history.push(`${match.url}/page/${page}/size/${count}`);
  }, [filter, history, match]);

  const handleFilterChange = useCallback(
    filter => {
      const first = (_pageNumber - 1) * _pageSize;
      if (_pageNumber && first !== filter.first) {
        filter.first = first;
      }
      setFilter(filter);
      onFilterChange(filter);
    },
    [setFilter, onFilterChange, _pageNumber, _pageSize],
  );
  const customTitle = useMemo(() => {
    if (location.pathname.includes('contents/add')) {
      return t('Add Content');
    } else if (location.pathname.endsWith('items')) {
      return t('Items');
    }
    return undefined;
  }, [location, t]);

  const title = useMemo(
    () =>
      location.pathname.endsWith('add/details') ||
      location.pathname.endsWith('add')
        ? 'Item'
        : t('Items'),
    [location, t],
  );

  return (
    <GqlFullCrudTable<
      IEContentItem,
      {
        libraryId?: number;
        libraryName?: string;
        selectedResources?: Partial<IEContentResource>[];
        goToCreateItem: () => void;
        usedKeywords?: string[];
      }
    >
      hasFilters
      hasPagination
      customTitle={customTitle}
      form={{
        component: EContentItemForm,
        default: {
          status: Active.value,
        },
        props: {
          libraryId,
          libraryName,
          selectedResources,
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          onGoBack: handleGoBack,
          goToCreateItem: goToCreate,
          usedKeywords,
        },
      }}
      gql={{
        query: eContentItems,
        single: eContentItem,
        count: eContentItemsCount,
        create: createEContentItem,
        update: updateEContentItem,
        delete: deleteEContentItem,
        cookQueryVariables,
        isListItemQueryPrevented,
      }}
      list={{
        hasFormActions: true,
        rightActionButtons,
        filterKey,
        canDelete,
        onFilterChange: handleFilterChange,
        filterComponent: {
          resources: FilterBar.EContentLibraryResourcesTreeSelector,
          languageId: languagesMultiSelector,
          status: statusWithDraftMultiSelector,
        },
        filterComponentProps: {
          resources: {
            libraryId,
            syntheticRootNodeName: libraryName,
            cookModels: cookFilterModels,
            initialStatus: Active.value,
            hasFilter: true,
            isSingleSelector: false,
            selectionFilter,
            initialSelectAll: true,
          },
          languageId: {
            filterOptions: filterLanguages,
            isEmptyAllowed: true,
            filterKey,
          },
          status: {
            hasAllOption: true,
          },
          initialFilter: {
            status: null,
            languageId: null,
            searchQuery: '',
            resources: [],
          },
        },
        tableConfig: {
          columns,
        },
      }}
      title={title}
    />
  );
};

export default ItemsView;

const canDelete = () => false;

const selectionFilter = nodes => filter(nodes, isEContentLibraryResource);
